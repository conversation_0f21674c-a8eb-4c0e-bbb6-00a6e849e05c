import * as React from 'react';
import { StyleSheet, ScrollView, View, SafeAreaView } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useShotManagement } from '@/hooks/useShotManagement';
import { useFocusEffect } from '@react-navigation/native';

interface HoleStats {
  holeNumber: number;
  totalShots: number;
  putts: number;
}

export default function ScorecardScreen() {
  const { shots } = useShotManagement();
  const [refreshKey, setRefreshKey] = React.useState(0);

  // Refresh data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      setRefreshKey(prev => prev + 1);
    }, [])
  );

  // Calculate hole statistics from actual shot data
  const holeStats: HoleStats[] = React.useMemo(() => {
    const stats: HoleStats[] = [];

    for (let holeNumber = 1; holeNumber <= 18; holeNumber++) {
      const holeShots = shots.filter(shot => shot.holeNumber === holeNumber);
      const totalShots = holeShots.length;
      const putts = holeShots.filter(shot => shot.club === 'Putter').length;

      stats.push({
        holeNumber,
        totalShots,
        putts,
      });
    }

    return stats;
  }, [shots, refreshKey]);

  const totalShots = holeStats.reduce((sum, hole) => sum + hole.totalShots, 0);
  const totalPutts = holeStats.reduce((sum, hole) => sum + hole.putts, 0);

  return (
    <SafeAreaView style={styles.container}>
      {/* Compact Summary Header */}
      <ThemedView style={styles.summaryContainer}>
        <ThemedText type="title" style={styles.title}>Golf Scorecard</ThemedText>
      </ThemedView>

      <ThemedView style={styles.tableContainer}>
        {/* Table Footer with Totals - Moved to top */}
        <View style={styles.tableFooter}>
          <ThemedText style={[styles.tableFooterText, styles.holeColumn]}>Total</ThemedText>
          <ThemedText style={[styles.tableFooterText, styles.shotsColumn]}>{totalShots}</ThemedText>
          <ThemedText style={[styles.tableFooterText, styles.puttsColumn]}>{totalPutts}</ThemedText>
        </View>

        {/* Table Header */}
        <View style={styles.tableHeader}>
          <ThemedText style={[styles.tableHeaderText, styles.holeColumn]}>Hole</ThemedText>
          <ThemedText style={[styles.tableHeaderText, styles.shotsColumn]}>Shots</ThemedText>
          <ThemedText style={[styles.tableHeaderText, styles.puttsColumn]}>Putts</ThemedText>
        </View>

        {/* Table Rows */}
        <ScrollView style={styles.tableScrollView}>
          {holeStats.map((hole) => (
            <View key={`hole-${hole.holeNumber}`} style={styles.tableRow}>
              <ThemedText style={[styles.tableCellText, styles.holeColumn]}>
                {hole.holeNumber}
              </ThemedText>
              <ThemedText style={[styles.tableCellText, styles.shotsColumn]}>
                {hole.totalShots || '-'}
              </ThemedText>
              <ThemedText style={[styles.tableCellText, styles.puttsColumn]}>
                {hole.putts || '-'}
              </ThemedText>
            </View>
          ))}
        </ScrollView>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  summaryContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summary: {
    fontSize: 16,
  },
  tableContainer: {
    flex: 1,
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 2,
    borderBottomColor: '#ddd',
  },
  tableHeaderText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tableScrollView: {
    maxHeight: 400,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tableCellText: {
    fontSize: 14,
    textAlign: 'center',
  },
  tableFooter: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 2,
    borderBottomColor: '#ddd',
  },
  tableFooterText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  holeColumn: {
    flex: 1,
  },
  shotsColumn: {
    flex: 1,
  },
  puttsColumn: {
    flex: 1,
  },
});
